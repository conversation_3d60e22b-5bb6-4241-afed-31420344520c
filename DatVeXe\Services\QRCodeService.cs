using System.Text.Json;
using DatVeXe.Models;

namespace DatVeXe.Services
{
    public interface IQRCodeService
    {
        string GenerateTicketQRData(Ve ve);
        string GenerateTicketQRData(string ticketCode, string passengerName, string phone, 
            int tripId, string seatNumber, DateTime departureTime, string route, decimal amount);
        TicketQRData? ParseTicketQRData(string qrData);
        bool ValidateTicketQR(string qrData, out TicketQRData? ticketData, out string errorMessage);
    }

    public class QRCodeService : IQRCodeService
    {
        private readonly ILogger<QRCodeService> _logger;

        public QRCodeService(ILogger<QRCodeService> logger)
        {
            _logger = logger;
        }

        public string GenerateTicketQRData(Ve ve)
        {
            var qrData = new TicketQRData
            {
                TicketCode = ve.MaVe,
                PassengerName = ve.TenKhach,
                Phone = ve.SoDienThoai,
                TripId = ve.ChuyenXeId,
                SeatNumber = ve.ChoNgoi?.SoGhe ?? "",
                DepartureTime = ve.ChuyenXe?.NgayKhoiHanh ?? DateTime.MinValue,
                Route = ve.ChuyenXe != null ? $"{ve.ChuyenXe.DiemDiDisplay} - {ve.ChuyenXe.DiemDenDisplay}" : "",
                Amount = ve.GiaVe,
                IssueTime = ve.NgayDat,
                Status = ve.VeTrangThai.ToString(),
                Version = "1.0"
            };

            return JsonSerializer.Serialize(qrData, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }

        public string GenerateTicketQRData(string ticketCode, string passengerName, string phone, 
            int tripId, string seatNumber, DateTime departureTime, string route, decimal amount)
        {
            var qrData = new TicketQRData
            {
                TicketCode = ticketCode,
                PassengerName = passengerName,
                Phone = phone,
                TripId = tripId,
                SeatNumber = seatNumber,
                DepartureTime = departureTime,
                Route = route,
                Amount = amount,
                IssueTime = DateTime.Now,
                Status = "DaDat",
                Version = "1.0"
            };

            return JsonSerializer.Serialize(qrData, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }

        public TicketQRData? ParseTicketQRData(string qrData)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<TicketQRData>(qrData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing QR data: {QRData}", qrData);
                return null;
            }
        }

        public bool ValidateTicketQR(string qrData, out TicketQRData? ticketData, out string errorMessage)
        {
            ticketData = null;
            errorMessage = string.Empty;

            try
            {
                ticketData = ParseTicketQRData(qrData);
                if (ticketData == null)
                {
                    errorMessage = "Không thể đọc thông tin QR code";
                    return false;
                }

                // Validate required fields
                if (string.IsNullOrEmpty(ticketData.TicketCode))
                {
                    errorMessage = "Mã vé không hợp lệ";
                    return false;
                }

                if (string.IsNullOrEmpty(ticketData.PassengerName))
                {
                    errorMessage = "Tên hành khách không hợp lệ";
                    return false;
                }

                if (ticketData.TripId <= 0)
                {
                    errorMessage = "Thông tin chuyến xe không hợp lệ";
                    return false;
                }

                // Validate departure time
                if (ticketData.DepartureTime == DateTime.MinValue)
                {
                    errorMessage = "Thời gian khởi hành không hợp lệ";
                    return false;
                }

                // Check if ticket is expired (for demo, allow tickets up to 1 day after departure)
                if (ticketData.DepartureTime.AddDays(1) < DateTime.Now)
                {
                    errorMessage = "Vé đã hết hạn";
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating QR data");
                errorMessage = "Lỗi xử lý QR code";
                return false;
            }
        }
    }

    public class TicketQRData
    {
        public string TicketCode { get; set; } = string.Empty;
        public string PassengerName { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public int TripId { get; set; }
        public string SeatNumber { get; set; } = string.Empty;
        public DateTime DepartureTime { get; set; }
        public string Route { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public DateTime IssueTime { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0";
    }
}
