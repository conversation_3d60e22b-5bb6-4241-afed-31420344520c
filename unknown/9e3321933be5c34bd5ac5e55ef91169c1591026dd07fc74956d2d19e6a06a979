using DatVeXe.Models;
using System.Text;
using System.Security.Cryptography;

namespace DatVeXe.Services
{
    public class PaymentService : IPaymentService
    {
        private readonly ILogger<PaymentService> _logger;
        private readonly IConfiguration _configuration;

        public PaymentService(ILogger<PaymentService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<PaymentResult> CreatePaymentAsync(PaymentRequest request)
        {
            try
            {
                _logger.LogInformation($"Creating payment for transaction: {request.TicketId}");

                switch (request.Method)
                {
                    case PhuongThucThanhToan.VNPay:
                        return await CreateVNPayPaymentAsync(request);

                    case PhuongThucThanhToan.MoMo:
                        return await CreateMoMoPaymentAsync(request);

                    case PhuongThucThanhToan.ZaloPay:
                        return await CreateZaloPayPaymentAsync(request);

                    case PhuongThucThanhToan.ChuyenKhoan:
                        return await CreateBankTransferAsync(request);

                    case PhuongThucThanhToan.TaiQuay:
                        return PaymentResult.CreateSuccess("Vé đã được đặt. Vui lòng thanh toán tại quầy.");

                    default:
                        return PaymentResult.CreateFailed("Phương thức thanh toán không được hỗ trợ");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment");
                return PaymentResult.CreateFailed("Có lỗi xảy ra khi tạo thanh toán");
            }
        }

        public async Task<PaymentResult> ProcessPaymentCallbackAsync(string transactionId, Dictionary<string, string> parameters)
        {
            try
            {
                _logger.LogInformation($"Processing payment callback for transaction: {transactionId}");
                _logger.LogInformation($"Callback parameters: {string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}"))}");

                // Determine payment method based on parameters
                var paymentMethod = DeterminePaymentMethod(parameters);
                _logger.LogInformation($"Detected payment method: {paymentMethod}");

                PaymentResult response;

                switch (paymentMethod)
                {
                    case "VNPay":
                        response = await ProcessVNPayCallbackAsync(transactionId, parameters);
                        break;
                    case "MoMo":
                        response = await ProcessMoMoCallbackAsync(transactionId, parameters);
                        break;
                    case "ZaloPay":
                        response = await ProcessZaloPayCallbackAsync(transactionId, parameters);
                        break;
                    default:
                        // Demo mode or unknown method
                        response = await ProcessDemoCallbackAsync(transactionId, parameters);
                        break;
                }

                _logger.LogInformation($"Payment callback result: Success={response.Success}, Message={response.Message}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing payment callback for transaction: {transactionId}");
                return PaymentResult.CreateFailed("Lỗi xử lý callback thanh toán", "CALLBACK_ERROR");
            }
        }

        private string DeterminePaymentMethod(Dictionary<string, string> parameters)
        {
            if (parameters.ContainsKey("vnp_ResponseCode"))
                return "VNPay";
            if (parameters.ContainsKey("resultCode"))
                return "MoMo";
            if (parameters.ContainsKey("return_code"))
                return "ZaloPay";
            return "Demo";
        }

        private async Task<PaymentResult> ProcessVNPayCallbackAsync(string transactionId, Dictionary<string, string> parameters)
        {
            await Task.Delay(100); // Simulate processing

            var responseCode = parameters.GetValueOrDefault("vnp_ResponseCode");
            var success = responseCode == "00"; // VNPay success code

            var result = success
                ? PaymentResult.CreateSuccess("Thanh toán VNPay thành công", null, parameters.GetValueOrDefault("vnp_TxnRef") ?? transactionId)
                : PaymentResult.CreateFailed($"Thanh toán VNPay thất bại (Mã lỗi: {responseCode})", responseCode);

            result.PaymentTime = DateTime.Now;
            result.AdditionalData = parameters;
            return result;
        }

        private async Task<PaymentResult> ProcessMoMoCallbackAsync(string transactionId, Dictionary<string, string> parameters)
        {
            await Task.Delay(100); // Simulate processing

            var resultCode = parameters.GetValueOrDefault("resultCode");
            var success = resultCode == "0"; // MoMo success code

            var result = success
                ? PaymentResult.CreateSuccess("Thanh toán MoMo thành công", null, parameters.GetValueOrDefault("orderId") ?? transactionId)
                : PaymentResult.CreateFailed($"Thanh toán MoMo thất bại (Mã lỗi: {resultCode})", resultCode);

            result.PaymentTime = DateTime.Now;
            result.AdditionalData = parameters;
            return result;
        }

        private async Task<PaymentResult> ProcessZaloPayCallbackAsync(string transactionId, Dictionary<string, string> parameters)
        {
            await Task.Delay(100); // Simulate processing

            var returnCode = parameters.GetValueOrDefault("return_code");
            var success = returnCode == "1"; // ZaloPay success code

            var result = success
                ? PaymentResult.CreateSuccess("Thanh toán ZaloPay thành công", null, parameters.GetValueOrDefault("app_trans_id") ?? transactionId)
                : PaymentResult.CreateFailed($"Thanh toán ZaloPay thất bại (Mã lỗi: {returnCode})", returnCode);

            result.PaymentTime = DateTime.Now;
            result.AdditionalData = parameters;
            return result;
        }

        private async Task<PaymentResult> ProcessDemoCallbackAsync(string transactionId, Dictionary<string, string> parameters)
        {
            await Task.Delay(100); // Simulate processing

            var status = parameters.GetValueOrDefault("status");
            var success = status == "success";

            var result = success
                ? PaymentResult.CreateSuccess("Thanh toán demo thành công", null, parameters.GetValueOrDefault("transaction_id") ?? transactionId)
                : PaymentResult.CreateFailed("Thanh toán demo thất bại", "DEMO_FAILED");

            result.PaymentTime = DateTime.Now;
            result.AdditionalData = parameters;
            return result;
        }

        public async Task<PaymentResult> CheckPaymentStatusAsync(string transactionId)
        {
            try
            {
                _logger.LogInformation($"Checking payment status for transaction: {transactionId}");

                // Demo mode - simulate payment status check
                await Task.Delay(100);

                var result = PaymentResult.CreateSuccess("Trạng thái thanh toán đã được cập nhật", null, transactionId);
                result.PaymentTime = DateTime.Now;
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking payment status for transaction: {transactionId}");
                return PaymentResult.CreateFailed("Lỗi kiểm tra trạng thái thanh toán");
            }
        }

        public async Task<bool> RefundPaymentAsync(string transactionId, decimal amount, string reason)
        {
            try
            {
                _logger.LogInformation($"Processing refund for transaction: {transactionId}, amount: {amount}");

                // Demo mode - simulate refund
                await Task.Delay(100);

                _logger.LogInformation($"Refund processed successfully for transaction: {transactionId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing refund for transaction: {transactionId}");
                return false;
            }
        }

        public string GeneratePaymentUrl(PhuongThucThanhToan method, string transactionId, decimal amount, string orderInfo, string returnUrl)
        {
            switch (method)
            {
                case PhuongThucThanhToan.VNPay:
                    return GenerateVNPayUrl(transactionId, amount, orderInfo, returnUrl);
                
                case PhuongThucThanhToan.MoMo:
                    return GenerateMoMoUrl(transactionId, amount, orderInfo, returnUrl);
                
                case PhuongThucThanhToan.ZaloPay:
                    return GenerateZaloPayUrl(transactionId, amount, orderInfo, returnUrl);
                
                default:
                    return returnUrl;
            }
        }

        private async Task<PaymentResult> CreateVNPayPaymentAsync(PaymentRequest request)
        {
            // Demo implementation - redirect to demo payment page
            var baseUrl = GetBaseUrl();
            var paymentUrl = $"{baseUrl}/Booking/DemoPayment?method=VNPay&transactionId={request.TicketId}&amount={request.Amount}&orderInfo={Uri.EscapeDataString(request.OrderInfo ?? "")}&returnUrl={Uri.EscapeDataString(request.ReturnUrl ?? "")}&cancelUrl={Uri.EscapeDataString(request.CancelUrl ?? "")}";

            await Task.Delay(100); // Simulate async operation
            return PaymentResult.CreateSuccess("Chuyển hướng đến VNPay Demo", paymentUrl, request.TicketId.ToString());
        }

        private async Task<PaymentResult> CreateMoMoPaymentAsync(PaymentRequest request)
        {
            // Demo implementation - redirect to demo payment page
            var baseUrl = GetBaseUrl();
            var paymentUrl = $"{baseUrl}/Booking/DemoPayment?method=MoMo&transactionId={request.TicketId}&amount={request.Amount}&orderInfo={Uri.EscapeDataString(request.OrderInfo ?? "")}&returnUrl={Uri.EscapeDataString(request.ReturnUrl ?? "")}&cancelUrl={Uri.EscapeDataString(request.CancelUrl ?? "")}";

            await Task.Delay(100); // Simulate async operation
            return PaymentResult.CreateSuccess("Chuyển hướng đến MoMo Demo", paymentUrl, request.TicketId.ToString());
        }

        private async Task<PaymentResult> CreateZaloPayPaymentAsync(PaymentRequest request)
        {
            // Demo implementation - redirect to demo payment page
            var baseUrl = GetBaseUrl();
            var paymentUrl = $"{baseUrl}/Booking/DemoPayment?method=ZaloPay&transactionId={request.TicketId}&amount={request.Amount}&orderInfo={Uri.EscapeDataString(request.OrderInfo ?? "")}&returnUrl={Uri.EscapeDataString(request.ReturnUrl ?? "")}&cancelUrl={Uri.EscapeDataString(request.CancelUrl ?? "")}";

            await Task.Delay(100); // Simulate async operation
            return PaymentResult.CreateSuccess("Chuyển hướng đến ZaloPay Demo", paymentUrl, request.TicketId.ToString());
        }

        private string GetBaseUrl()
        {
            // In a real application, this should be injected or configured
            return "http://localhost:5057";
        }

        private async Task<PaymentResult> CreateBankTransferAsync(PaymentRequest request)
        {
            // For bank transfer, provide bank account information
            await Task.Delay(100); // Simulate async operation
            return PaymentResult.CreateSuccess("Vui lòng chuyển khoản theo thông tin được cung cấp", null, request.TicketId.ToString());
        }

        private string GenerateVNPayUrl(string transactionId, decimal amount, string orderInfo, string returnUrl)
        {
            // Demo mode - simulate payment gateway by redirecting to callback with success parameters
            var encodedReturnUrl = Uri.EscapeDataString(returnUrl);
            return $"{returnUrl}?vnp_ResponseCode=00&vnp_TxnRef={transactionId}&vnp_Amount={amount * 100}&vnp_TransactionStatus=00&demo=true";
        }

        private string GenerateMoMoUrl(string transactionId, decimal amount, string orderInfo, string returnUrl)
        {
            // Demo mode - simulate payment gateway by redirecting to callback with success parameters
            var encodedReturnUrl = Uri.EscapeDataString(returnUrl);
            return $"{returnUrl}?resultCode=0&orderId={transactionId}&amount={amount}&message=Success&demo=true";
        }

        private string GenerateZaloPayUrl(string transactionId, decimal amount, string orderInfo, string returnUrl)
        {
            // Demo mode - simulate payment gateway by redirecting to callback with success parameters
            var encodedReturnUrl = Uri.EscapeDataString(returnUrl);
            return $"{returnUrl}?status=1&app_trans_id={transactionId}&amount={amount}&demo=true";
        }
    }
}
