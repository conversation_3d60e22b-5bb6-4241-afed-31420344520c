using DatVeXe.Models;
using DatVeXe.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace DatVeXe.Controllers
{
    public class XeController : Controller
    {
        private readonly DatVeXeContext _context;
        public XeController(DatVeXeContext context)
        {
            _context = context;
        }

        // GET: Xe
        public async Task<IActionResult> Index()
        {
            var xes = await _context.Xes
                .Include(x => x.ChuyenXes)
                .OrderBy(x => x.BienSo)
                .ToListAsync();
            return View(xes);
        }

        // GET: Xe/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                TempData["Error"] = "Không tìm thấy xe";
                return RedirectToAction(nameof(Index));
            }

            var xe = await _context.Xes
                .Include(x => x.ChuyenXes)
                .ThenInclude(c => c.Ves)
                .FirstOrDefaultAsync(x => x.XeId == id);

            if (xe == null)
            {
                TempData["Error"] = "Không tìm thấy xe";
                return RedirectToAction(nameof(Index));
            }

            return View(xe);
        }

        // GET: Xe/Create
        public IActionResult Create()
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            return View();
        }

        // POST: Xe/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("BienSo,LoaiXe,SoGhe")] Xe xe)
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            if (ModelState.IsValid)
            {
                // Check if license plate already exists
                if (await _context.Xes.AnyAsync(x => x.BienSo == xe.BienSo))
                {
                    ModelState.AddModelError("BienSo", "Biển số xe đã tồn tại");
                    return View(xe);
                }

                try
                {
                    _context.Add(xe);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Thêm xe thành công";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception)
                {
                    TempData["Error"] = "Có lỗi xảy ra khi thêm xe";
                }
            }

            return View(xe);
        }

        // GET: Xe/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            if (id == null)
            {
                TempData["Error"] = "Không tìm thấy xe";
                return RedirectToAction(nameof(Index));
            }

            var xe = await _context.Xes.FindAsync(id);
            if (xe == null)
            {
                TempData["Error"] = "Không tìm thấy xe";
                return RedirectToAction(nameof(Index));
            }

            return View(xe);
        }

        // POST: Xe/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("XeId,BienSo,LoaiXe,SoGhe")] Xe xe)
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            if (id != xe.XeId)
            {
                TempData["Error"] = "Không tìm thấy xe";
                return RedirectToAction(nameof(Index));
            }

            if (ModelState.IsValid)
            {
                // Check if license plate already exists (excluding current record)
                if (await _context.Xes.AnyAsync(x => x.BienSo == xe.BienSo && x.XeId != xe.XeId))
                {
                    ModelState.AddModelError("BienSo", "Biển số xe đã tồn tại");
                    return View(xe);
                }

                try
                {
                    _context.Update(xe);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Cập nhật xe thành công";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await XeExists(xe.XeId))
                    {
                        TempData["Error"] = "Không tìm thấy xe";
                        return RedirectToAction(nameof(Index));
                    }
                    else
                    {
                        throw;
                    }
                }
                catch (Exception)
                {
                    TempData["Error"] = "Có lỗi xảy ra khi cập nhật xe";
                }
            }

            return View(xe);
        }

        // GET: Xe/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            if (id == null)
            {
                TempData["Error"] = "Không tìm thấy xe";
                return RedirectToAction(nameof(Index));
            }

            var xe = await _context.Xes
                .Include(x => x.ChuyenXes)
                .FirstOrDefaultAsync(x => x.XeId == id);

            if (xe == null)
            {
                TempData["Error"] = "Không tìm thấy xe";
                return RedirectToAction(nameof(Index));
            }

            return View(xe);
        }

        // POST: Xe/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            var xe = await _context.Xes
                .Include(x => x.ChuyenXes)
                .FirstOrDefaultAsync(x => x.XeId == id);

            if (xe != null)
            {
                // Check if xe has any trips
                if (xe.ChuyenXes != null && xe.ChuyenXes.Any())
                {
                    TempData["Error"] = "Không thể xóa xe đã có chuyến đi";
                    return RedirectToAction(nameof(Index));
                }

                try
                {
                    _context.Xes.Remove(xe);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Xóa xe thành công";
                }
                catch (Exception)
                {
                    TempData["Error"] = "Có lỗi xảy ra khi xóa xe";
                }
            }

            return RedirectToAction(nameof(Index));
        }

        private async Task<bool> XeExists(int id)
        {
            return await _context.Xes.AnyAsync(e => e.XeId == id);
        }
    }

    public class ChuyenXeController : Controller
    {
        private readonly DatVeXeContext _context;
        public ChuyenXeController(DatVeXeContext context)
        {
            _context = context;
        }



        public async Task<IActionResult> Index(ChuyenXeSearchViewModel? searchModel)
        {
            try
            {
                var query = _context.ChuyenXes
                    .Include(c => c.Xe)
                    .Include(c => c.Ves)
                    .Include(c => c.TuyenDuong)
                    .Include(c => c.TaiXe)
                    .AsQueryable();

                // Áp dụng các bộ lọc nếu có
                if (searchModel != null)
                {
                    if (!string.IsNullOrEmpty(searchModel.DiemDi))
                    {
                        query = query.Where(c => (c.TuyenDuong != null ? c.TuyenDuong.DiemDi : c.DiemDi ?? "").Contains(searchModel.DiemDi));
                    }

                    if (!string.IsNullOrEmpty(searchModel.DiemDen))
                    {
                        query = query.Where(c => (c.TuyenDuong != null ? c.TuyenDuong.DiemDen : c.DiemDen ?? "").Contains(searchModel.DiemDen));
                    }

                    if (searchModel.TuNgay.HasValue)
                    {
                        query = query.Where(c => c.NgayKhoiHanh.Date >= searchModel.TuNgay.Value.Date);
                    }

                    if (searchModel.DenNgay.HasValue)
                    {
                        query = query.Where(c => c.NgayKhoiHanh.Date <= searchModel.DenNgay.Value.Date);
                    }

                    // Lọc theo giá
                    if (searchModel.GiaTu.HasValue)
                    {
                        query = query.Where(c => c.Gia >= searchModel.GiaTu.Value);
                    }

                    if (searchModel.GiaDen.HasValue)
                    {
                        query = query.Where(c => c.Gia <= searchModel.GiaDen.Value);
                    }

                    // Lọc theo loại xe
                    if (!string.IsNullOrEmpty(searchModel.LoaiXe))
                    {
                        query = query.Where(c => c.Xe != null && c.Xe.LoaiXe.Contains(searchModel.LoaiXe));
                    }

                    if (!string.IsNullOrEmpty(searchModel.TrangThai))
                    {
                        switch (searchModel.TrangThai)
                        {
                            case "chua_khoi_hanh":
                                query = query.Where(c => c.NgayKhoiHanh > DateTime.Now);
                                break;
                            case "da_khoi_hanh":
                                query = query.Where(c => c.NgayKhoiHanh <= DateTime.Now);
                                break;
                            // "all" - không lọc gì
                        }
                    }
                }

                // Áp dụng sắp xếp
                var sortBy = searchModel?.SortBy ?? "NgayKhoiHanh";
                var sortOrder = searchModel?.SortOrder ?? "asc";

                switch (sortBy.ToLower())
                {
                    case "gia":
                        query = sortOrder == "desc" ? query.OrderByDescending(c => c.Gia) : query.OrderBy(c => c.Gia);
                        break;
                    case "diemdi":
                        query = sortOrder == "desc" ? query.OrderByDescending(c => c.TuyenDuong != null ? c.TuyenDuong.DiemDi : c.DiemDi ?? "") : query.OrderBy(c => c.TuyenDuong != null ? c.TuyenDuong.DiemDi : c.DiemDi ?? "");
                        break;
                    case "diemden":
                        query = sortOrder == "desc" ? query.OrderByDescending(c => c.TuyenDuong != null ? c.TuyenDuong.DiemDen : c.DiemDen ?? "") : query.OrderBy(c => c.TuyenDuong != null ? c.TuyenDuong.DiemDen : c.DiemDen ?? "");
                        break;
                    case "loaixe":
                        query = sortOrder == "desc" ? query.OrderByDescending(c => c.Xe != null ? c.Xe.LoaiXe : "") : query.OrderBy(c => c.Xe != null ? c.Xe.LoaiXe : "");
                        break;
                    default: // NgayKhoiHanh
                        query = sortOrder == "desc" ? query.OrderByDescending(c => c.NgayKhoiHanh) : query.OrderBy(c => c.NgayKhoiHanh);
                        break;
                }

                var chuyens = await query.ToListAsync();

                // Lọc theo ghế trống nếu cần (phải làm sau khi load data vì phức tạp)
                if (searchModel?.CoGheTrong == true)
                {
                    chuyens = chuyens.Where(c => c.Xe != null && c.Xe.SoGhe > (c.Ves?.Count ?? 0)).ToList();
                }

                // Tạo ViewModel để trả về
                var viewModel = new ChuyenXeSearchViewModel
                {
                    DiemDi = searchModel?.DiemDi,
                    DiemDen = searchModel?.DiemDen,
                    TuNgay = searchModel?.TuNgay,
                    DenNgay = searchModel?.DenNgay,
                    GiaTu = searchModel?.GiaTu,
                    GiaDen = searchModel?.GiaDen,
                    LoaiXe = searchModel?.LoaiXe,
                    TrangThai = searchModel?.TrangThai,
                    CoGheTrong = searchModel?.CoGheTrong,
                    SortBy = searchModel?.SortBy ?? "NgayKhoiHanh",
                    SortOrder = searchModel?.SortOrder ?? "asc",
                    KetQua = chuyens
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Có lỗi xảy ra khi tải dữ liệu: " + ex.Message;
                return View(new ChuyenXeSearchViewModel { KetQua = new List<ChuyenXe>() });
            }
        }

        // Action để export Excel
        public async Task<IActionResult> ExportExcel(ChuyenXeSearchViewModel? searchModel)
        {
            var query = _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.Ves)
                .Include(c => c.TuyenDuong)
                .AsQueryable();

            // Áp dụng các bộ lọc giống như Index
            if (searchModel != null)
            {
                if (!string.IsNullOrEmpty(searchModel.DiemDi))
                {
                    query = query.Where(c => (c.TuyenDuong != null ? c.TuyenDuong.DiemDi : c.DiemDi).Contains(searchModel.DiemDi));
                }

                if (!string.IsNullOrEmpty(searchModel.DiemDen))
                {
                    query = query.Where(c => (c.TuyenDuong != null ? c.TuyenDuong.DiemDen : c.DiemDen).Contains(searchModel.DiemDen));
                }

                if (searchModel.TuNgay.HasValue)
                {
                    query = query.Where(c => c.NgayKhoiHanh.Date >= searchModel.TuNgay.Value.Date);
                }

                if (searchModel.DenNgay.HasValue)
                {
                    query = query.Where(c => c.NgayKhoiHanh.Date <= searchModel.DenNgay.Value.Date);
                }

                if (!string.IsNullOrEmpty(searchModel.TrangThai))
                {
                    switch (searchModel.TrangThai)
                    {
                        case "chua_khoi_hanh":
                            query = query.Where(c => c.NgayKhoiHanh > DateTime.Now);
                            break;
                        case "da_khoi_hanh":
                            query = query.Where(c => c.NgayKhoiHanh <= DateTime.Now);
                            break;
                    }
                }
            }

            var chuyens = await query.OrderBy(c => c.NgayKhoiHanh).ToListAsync();

            // Lọc theo ghế trống nếu cần
            if (searchModel?.CoGheTrong == true)
            {
                chuyens = chuyens.Where(c => c.Xe.SoGhe > c.Ves.Count).ToList();
            }

            // Tạo CSV content
            var csv = new System.Text.StringBuilder();
            csv.AppendLine("Điểm đi,Điểm đến,Ngày khởi hành,Biển số xe,Loại xe,Số ghế,Số ghế trống,Trạng thái");

            foreach (var item in chuyens)
            {
                var soGheTrong = item.Xe.SoGhe - item.Ves.Count;
                var trangThai = item.NgayKhoiHanh <= DateTime.Now ? "Đã khởi hành" : "Chưa khởi hành";

                csv.AppendLine($"{item.DiemDiDisplay},{item.DiemDenDisplay},{item.NgayKhoiHanh:dd/MM/yyyy HH:mm},{item.Xe.BienSo},{item.Xe.LoaiXe},{item.Xe.SoGhe},{soGheTrong},{trangThai}");
            }

            var fileName = $"DanhSachChuyenXe_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            var bytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());

            return File(bytes, "text/csv", fileName);
        }



        // ❌ DEPRECATED: Action cho tìm kiếm nâng cao - Sử dụng BookingController.Search thay thế
        // Giữ lại để tương thích với các link cũ, redirect đến BookingController
        public IActionResult Search()
        {
            return RedirectToAction("Search", "Booking");
        }

        // ❌ DEPRECATED: POST Search - Redirect đến BookingController
        [HttpPost]
        public IActionResult Search(ChuyenXeSearchViewModel searchModel)
        {
            return RedirectToAction("Search", "Booking");
        }

        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                TempData["Error"] = "Không tìm thấy chuyến xe";
                return RedirectToAction(nameof(Index));
            }

            try
            {
                var chuyenXe = await _context.ChuyenXes
                    .Include(c => c.Xe)
                    .ThenInclude(x => x.ChoNgois)
                    .Include(c => c.Ves)
                    .ThenInclude(v => v.ChoNgoi)
                    .Include(c => c.TuyenDuong)
                    .Include(c => c.TaiXe)
                    .FirstOrDefaultAsync(c => c.ChuyenXeId == id);

                if (chuyenXe == null)
                {
                    TempData["Error"] = "Không tìm thấy chuyến xe";
                    return RedirectToAction(nameof(Index));
                }

                return View(chuyenXe);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Có lỗi xảy ra khi tải thông tin chuyến xe: " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        // Dashboard thống kê chuyến xe
        public async Task<IActionResult> Dashboard()
        {
            try
            {
                var now = DateTime.Now;
                var startOfMonth = new DateTime(now.Year, now.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

                // Thống kê tổng quan
                var tongChuyenXe = await _context.ChuyenXes.CountAsync();
                var chuyenXeHomNay = await _context.ChuyenXes
                    .CountAsync(c => c.NgayKhoiHanh.Date == now.Date);
                var chuyenXeThangNay = await _context.ChuyenXes
                    .CountAsync(c => c.NgayKhoiHanh >= startOfMonth && c.NgayKhoiHanh <= endOfMonth);
                var chuyenXeChuaKhoiHanh = await _context.ChuyenXes
                    .CountAsync(c => c.NgayKhoiHanh > now);

                // Thống kê theo tuyến đường phổ biến
                var topTuyenDuong = await _context.ChuyenXes
                    .Include(c => c.TuyenDuong)
                    .Include(c => c.Ves)
                    .Where(c => c.NgayKhoiHanh >= startOfMonth && c.NgayKhoiHanh <= endOfMonth)
                    .GroupBy(c => new { c.DiemDiDisplay, c.DiemDenDisplay })
                    .Select(g => new
                    {
                        TuyenDuong = g.Key.DiemDiDisplay + " - " + g.Key.DiemDenDisplay,
                        SoChuyenXe = g.Count(),
                        TongVe = g.Sum(c => c.Ves != null ? c.Ves.Count : 0),
                        DoanhThu = g.Sum(c => c.Ves != null ? c.Ves.Sum(v => v.GiaVe) : 0)
                    })
                    .OrderByDescending(t => t.SoChuyenXe)
                    .Take(5)
                    .ToListAsync();

                // Thống kê theo loại xe
                var thongKeLoaiXe = await _context.ChuyenXes
                    .Include(c => c.Xe)
                    .Include(c => c.Ves)
                    .Where(c => c.NgayKhoiHanh >= startOfMonth && c.NgayKhoiHanh <= endOfMonth)
                    .GroupBy(c => c.Xe.LoaiXe)
                    .Select(g => new
                    {
                        LoaiXe = g.Key,
                        SoChuyenXe = g.Count(),
                        TongVe = g.Sum(c => c.Ves != null ? c.Ves.Count : 0),
                        DoanhThu = g.Sum(c => c.Ves != null ? c.Ves.Sum(v => v.GiaVe) : 0)
                    })
                    .OrderByDescending(t => t.DoanhThu)
                    .ToListAsync();

                ViewBag.TongChuyenXe = tongChuyenXe;
                ViewBag.ChuyenXeHomNay = chuyenXeHomNay;
                ViewBag.ChuyenXeThangNay = chuyenXeThangNay;
                ViewBag.ChuyenXeChuaKhoiHanh = chuyenXeChuaKhoiHanh;
                ViewBag.TopTuyenDuong = topTuyenDuong;
                ViewBag.ThongKeLoaiXe = thongKeLoaiXe;

                return View();
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Có lỗi xảy ra khi tải thống kê: " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        // Trang tổng quan về ChuyenXe
        public IActionResult Overview()
        {
            return View();
        }

        public async Task<IActionResult> Create()
        {
            try
            {
                var xeList = await _context.Xes
                    .OrderBy(x => x.BienSo)
                    .ToListAsync();

                if (!xeList.Any())
                {
                    TempData["Error"] = "Chưa có xe nào trong hệ thống";
                    return RedirectToAction(nameof(Index));
                }

                var taiXeList = await _context.TaiXes
                    .Where(t => t.TrangThai == TrangThaiTaiXe.HoatDong)
                    .OrderBy(t => t.HoTen)
                    .ToListAsync();

                ViewBag.XeList = xeList.Select(x => new SelectListItem
                {
                    Value = x.XeId.ToString(),
                    Text = $"{x.BienSo} ({x.LoaiXe} - {x.SoGhe} chỗ)"
                }).ToList();

                ViewBag.TaiXeList = new List<SelectListItem>
                {
                    new SelectListItem { Value = "", Text = "-- Chọn tài xế --" }
                }.Concat(taiXeList.Select(t => new SelectListItem
                {
                    Value = t.TaiXeId.ToString(),
                    Text = $"{t.HoTen} - {t.SoDienThoai}"
                })).ToList();

                return View(new ChuyenXe { NgayKhoiHanh = DateTime.Now.AddHours(1) });
            }
            catch (Exception)
            {
                TempData["Error"] = "Có lỗi xảy ra khi tải dữ liệu";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("DiemDi,DiemDen,NgayKhoiHanh,XeId,TaiXeId")] ChuyenXe chuyenXe)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    if (chuyenXe.NgayKhoiHanh <= DateTime.Now)
                    {
                        ModelState.AddModelError("NgayKhoiHanh", "Ngày khởi hành phải lớn hơn ngày hiện tại");
                    }
                    else
                    {
                        var xe = await _context.Xes.FindAsync(chuyenXe.XeId);
                        if (xe == null)
                        {
                            ModelState.AddModelError("XeId", "Xe không tồn tại");
                        }
                        else
                        {
                            // Kiểm tra xem xe đã được sử dụng trong khoảng thời gian này chưa
                            var existingChuyen = await _context.ChuyenXes
                                .Where(c => c.XeId == chuyenXe.XeId)
                                .Where(c => c.NgayKhoiHanh.Date == chuyenXe.NgayKhoiHanh.Date)
                                .AnyAsync();

                            if (existingChuyen)
                            {
                                ModelState.AddModelError("XeId", "Xe đã được sử dụng trong ngày này");
                            }
                            else
                            {
                                _context.Add(chuyenXe);
                                await _context.SaveChangesAsync();
                                TempData["Success"] = "Thêm chuyến xe thành công!";
                                return RedirectToAction(nameof(Index));
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                ModelState.AddModelError("", "Có lỗi xảy ra khi lưu dữ liệu");
            }

            // Nếu có lỗi, load lại danh sách xe và tài xế
            var xeList = await _context.Xes.OrderBy(x => x.BienSo).ToListAsync();
            var taiXeList = await _context.TaiXes
                .Where(t => t.TrangThai == TrangThaiTaiXe.HoatDong)
                .OrderBy(t => t.HoTen)
                .ToListAsync();

            ViewBag.XeList = xeList.Select(x => new SelectListItem
            {
                Value = x.XeId.ToString(),
                Text = $"{x.BienSo} ({x.LoaiXe} - {x.SoGhe} chỗ)",
                Selected = (x.XeId == chuyenXe.XeId)
            }).ToList();

            ViewBag.TaiXeList = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "-- Chọn tài xế --" }
            }.Concat(taiXeList.Select(t => new SelectListItem
            {
                Value = t.TaiXeId.ToString(),
                Text = $"{t.HoTen} - {t.SoDienThoai}",
                Selected = (t.TaiXeId == chuyenXe.TaiXeId)
            })).ToList();

            return View(chuyenXe);
        }

        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                TempData["Error"] = "Không tìm thấy chuyến xe";
                return RedirectToAction(nameof(Index));
            }

            var chuyenXe = await _context.ChuyenXes
                .Include(c => c.Ves)
                .FirstOrDefaultAsync(c => c.ChuyenXeId == id);

            if (chuyenXe == null)
            {
                TempData["Error"] = "Không tìm thấy chuyến xe";
                return RedirectToAction(nameof(Index));
            }

            if (chuyenXe.NgayKhoiHanh <= DateTime.Now)
            {
                TempData["Error"] = "Không thể sửa chuyến xe đã khởi hành";
                return RedirectToAction(nameof(Index));
            }

            var xeList = await _context.Xes.OrderBy(x => x.BienSo).ToListAsync();
            var taiXeList = await _context.TaiXes
                .Where(t => t.TrangThai == TrangThaiTaiXe.HoatDong)
                .OrderBy(t => t.HoTen)
                .ToListAsync();

            ViewBag.XeList = xeList.Select(x => new SelectListItem
            {
                Value = x.XeId.ToString(),
                Text = $"{x.BienSo} ({x.LoaiXe} - {x.SoGhe} chỗ)",
                Selected = (x.XeId == chuyenXe.XeId)
            }).ToList();

            ViewBag.TaiXeList = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "-- Chọn tài xế --" }
            }.Concat(taiXeList.Select(t => new SelectListItem
            {
                Value = t.TaiXeId.ToString(),
                Text = $"{t.HoTen} - {t.SoDienThoai}",
                Selected = (t.TaiXeId == chuyenXe.TaiXeId)
            })).ToList();

            return View(chuyenXe);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("ChuyenXeId,DiemDi,DiemDen,NgayKhoiHanh,XeId,TaiXeId")] ChuyenXe chuyenXe)
        {
            if (id != chuyenXe.ChuyenXeId)
            {
                TempData["Error"] = "Không tìm thấy chuyến xe";
                return RedirectToAction(nameof(Index));
            }

            var existingChuyen = await _context.ChuyenXes
                .Include(c => c.Ves)
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.ChuyenXeId == id);

            if (existingChuyen == null)
            {
                TempData["Error"] = "Không tìm thấy chuyến xe";
                return RedirectToAction(nameof(Index));
            }

            if (existingChuyen.NgayKhoiHanh <= DateTime.Now)
            {
                TempData["Error"] = "Không thể sửa chuyến xe đã khởi hành";
                return RedirectToAction(nameof(Index));
            }

            if (ModelState.IsValid)
            {
                try
                {
                    if (chuyenXe.NgayKhoiHanh <= DateTime.Now)
                    {
                        ModelState.AddModelError("NgayKhoiHanh", "Ngày khởi hành phải lớn hơn ngày hiện tại");
                    }
                    else
                    {
                        var xe = await _context.Xes.FindAsync(chuyenXe.XeId);
                        if (xe == null)
                        {
                            ModelState.AddModelError("XeId", "Xe không tồn tại");
                        }
                        else
                        {
                            // Kiểm tra xem xe đã được sử dụng trong khoảng thời gian này chưa (trừ chuyến hiện tại)
                            var otherChuyen = await _context.ChuyenXes
                                .Where(c => c.XeId == chuyenXe.XeId && c.ChuyenXeId != chuyenXe.ChuyenXeId)
                                .Where(c => c.NgayKhoiHanh.Date == chuyenXe.NgayKhoiHanh.Date)
                                .AnyAsync();

                            if (otherChuyen)
                            {
                                ModelState.AddModelError("XeId", "Xe đã được sử dụng trong ngày này");
                            }
                            else
                            {
                                _context.Update(chuyenXe);
                                await _context.SaveChangesAsync();
                                TempData["Success"] = "Cập nhật chuyến xe thành công!";
                                return RedirectToAction(nameof(Index));
                            }
                        }
                    }
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await _context.ChuyenXes.AnyAsync(c => c.ChuyenXeId == chuyenXe.ChuyenXeId))
                    {
                        TempData["Error"] = "Không tìm thấy chuyến xe";
                        return RedirectToAction(nameof(Index));
                    }
                    else throw;
                }
            }

            var xeList = await _context.Xes.OrderBy(x => x.BienSo).ToListAsync();
            var taiXeList = await _context.TaiXes
                .Where(t => t.TrangThai == TrangThaiTaiXe.HoatDong)
                .OrderBy(t => t.HoTen)
                .ToListAsync();

            ViewBag.XeList = xeList.Select(x => new SelectListItem
            {
                Value = x.XeId.ToString(),
                Text = $"{x.BienSo} ({x.LoaiXe} - {x.SoGhe} chỗ)",
                Selected = (x.XeId == chuyenXe.XeId)
            }).ToList();

            ViewBag.TaiXeList = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "-- Chọn tài xế --" }
            }.Concat(taiXeList.Select(t => new SelectListItem
            {
                Value = t.TaiXeId.ToString(),
                Text = $"{t.HoTen} - {t.SoDienThoai}",
                Selected = (t.TaiXeId == chuyenXe.TaiXeId)
            })).ToList();

            return View(chuyenXe);
        }

        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                TempData["Error"] = "Không tìm thấy chuyến xe";
                return RedirectToAction(nameof(Index));
            }

            var chuyenXe = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.Ves)
                .FirstOrDefaultAsync(c => c.ChuyenXeId == id);

            if (chuyenXe == null)
            {
                TempData["Error"] = "Không tìm thấy chuyến xe";
                return RedirectToAction(nameof(Index));
            }

            if (chuyenXe.NgayKhoiHanh <= DateTime.Now)
            {
                TempData["Error"] = "Không thể xóa chuyến xe đã khởi hành";
                return RedirectToAction(nameof(Index));
            }

            if (chuyenXe.Ves.Any())
            {
                TempData["Error"] = "Không thể xóa chuyến xe đã có người đặt vé";
                return RedirectToAction(nameof(Index));
            }

            return View(chuyenXe);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var chuyenXe = await _context.ChuyenXes
                    .Include(c => c.Ves)
                    .FirstOrDefaultAsync(c => c.ChuyenXeId == id);

                if (chuyenXe == null)
                {
                    TempData["Error"] = "Không tìm thấy chuyến xe";
                    return RedirectToAction(nameof(Index));
                }

                if (chuyenXe.NgayKhoiHanh <= DateTime.Now)
                {
                    TempData["Error"] = "Không thể xóa chuyến xe đã khởi hành";
                    return RedirectToAction(nameof(Index));
                }

                if (chuyenXe.Ves != null && chuyenXe.Ves.Any())
                {
                    TempData["Error"] = "Không thể xóa chuyến xe đã có người đặt vé";
                    return RedirectToAction(nameof(Index));
                }

                _context.ChuyenXes.Remove(chuyenXe);
                await _context.SaveChangesAsync();
                TempData["Success"] = "Xóa chuyến xe thành công!";
            }
            catch (Exception)
            {
                TempData["Error"] = "Có lỗi xảy ra khi xóa chuyến xe";
            }

            return RedirectToAction(nameof(Index));
        }
    }











    public class NguoiDungController : Controller
    {
        private readonly DatVeXeContext _context;
        public NguoiDungController(DatVeXeContext context)
        {
            _context = context;
        }

        // GET: NguoiDung (Admin only)
        public async Task<IActionResult> Index()
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            var users = await _context.NguoiDungs
                .OrderBy(u => u.HoTen)
                .ToListAsync();
            return View(users);
        }

        // GET: NguoiDung/Details/5 (Admin only)
        public async Task<IActionResult> Details(int? id)
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            if (id == null)
            {
                TempData["Error"] = "Không tìm thấy người dùng";
                return RedirectToAction(nameof(Index));
            }

            var nguoiDung = await _context.NguoiDungs
                .FirstOrDefaultAsync(n => n.NguoiDungId == id);

            if (nguoiDung == null)
            {
                TempData["Error"] = "Không tìm thấy người dùng";
                return RedirectToAction(nameof(Index));
            }

            // Get user's booking statistics (removed Ve references)
            ViewBag.UserTickets = new List<object>(); // Empty list since Ve is removed
            ViewBag.TotalTickets = 0;
            ViewBag.TotalSpent = 0;

            return View(nguoiDung);
        }

        // GET: NguoiDung/Create (Admin only)
        public IActionResult Create()
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            return View();
        }

        // POST: NguoiDung/Create (Admin only)
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("HoTen,Email,MatKhau,LaAdmin")] NguoiDung nguoiDung)
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            if (ModelState.IsValid)
            {
                // Check if email already exists
                if (await _context.NguoiDungs.AnyAsync(u => u.Email == nguoiDung.Email))
                {
                    ModelState.AddModelError("Email", "Email đã tồn tại");
                    return View(nguoiDung);
                }

                try
                {
                    // Hash password
                    nguoiDung.MatKhau = HashPassword(nguoiDung.MatKhau);

                    _context.Add(nguoiDung);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Thêm người dùng thành công";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception)
                {
                    TempData["Error"] = "Có lỗi xảy ra khi thêm người dùng";
                }
            }

            return View(nguoiDung);
        }

        // GET: NguoiDung/Edit/5 (Admin only)
        public async Task<IActionResult> Edit(int? id)
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            if (id == null)
            {
                TempData["Error"] = "Không tìm thấy người dùng";
                return RedirectToAction(nameof(Index));
            }

            var nguoiDung = await _context.NguoiDungs.FindAsync(id);
            if (nguoiDung == null)
            {
                TempData["Error"] = "Không tìm thấy người dùng";
                return RedirectToAction(nameof(Index));
            }

            // Don't show password in edit form
            nguoiDung.MatKhau = "";
            return View(nguoiDung);
        }

        // POST: NguoiDung/Edit/5 (Admin only)
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("NguoiDungId,HoTen,Email,MatKhau,LaAdmin")] NguoiDung nguoiDung)
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            if (id != nguoiDung.NguoiDungId)
            {
                TempData["Error"] = "Không tìm thấy người dùng";
                return RedirectToAction(nameof(Index));
            }

            // Get current user data
            var currentUser = await _context.NguoiDungs.AsNoTracking().FirstOrDefaultAsync(u => u.NguoiDungId == id);
            if (currentUser == null)
            {
                TempData["Error"] = "Không tìm thấy người dùng";
                return RedirectToAction(nameof(Index));
            }

            // Remove password validation if not provided
            if (string.IsNullOrEmpty(nguoiDung.MatKhau))
            {
                ModelState.Remove("MatKhau");
                nguoiDung.MatKhau = currentUser.MatKhau; // Keep current password
            }
            else
            {
                nguoiDung.MatKhau = HashPassword(nguoiDung.MatKhau); // Hash new password
            }

            if (ModelState.IsValid)
            {
                // Check if email already exists (excluding current record)
                if (await _context.NguoiDungs.AnyAsync(u => u.Email == nguoiDung.Email && u.NguoiDungId != nguoiDung.NguoiDungId))
                {
                    ModelState.AddModelError("Email", "Email đã tồn tại");
                    nguoiDung.MatKhau = ""; // Clear password for security
                    return View(nguoiDung);
                }

                try
                {
                    _context.Update(nguoiDung);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Cập nhật người dùng thành công";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await NguoiDungExists(nguoiDung.NguoiDungId))
                    {
                        TempData["Error"] = "Không tìm thấy người dùng";
                        return RedirectToAction(nameof(Index));
                    }
                    else
                    {
                        throw;
                    }
                }
                catch (Exception)
                {
                    TempData["Error"] = "Có lỗi xảy ra khi cập nhật người dùng";
                }
            }

            nguoiDung.MatKhau = ""; // Clear password for security
            return View(nguoiDung);
        }

        // GET: NguoiDung/Delete/5 (Admin only)
        public async Task<IActionResult> Delete(int? id)
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            if (id == null)
            {
                TempData["Error"] = "Không tìm thấy người dùng";
                return RedirectToAction(nameof(Index));
            }

            var nguoiDung = await _context.NguoiDungs
                .FirstOrDefaultAsync(n => n.NguoiDungId == id);

            if (nguoiDung == null)
            {
                TempData["Error"] = "Không tìm thấy người dùng";
                return RedirectToAction(nameof(Index));
            }

            // Check if user has bookings (removed Ve references)
            ViewBag.HasBookings = false;

            return View(nguoiDung);
        }

        // POST: NguoiDung/Delete/5 (Admin only)
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            // Check if user is admin
            if (HttpContext.Session.GetInt32("IsAdmin") != 1)
            {
                TempData["Error"] = "Bạn không có quyền truy cập chức năng này";
                return RedirectToAction("Index", "Home");
            }

            var nguoiDung = await _context.NguoiDungs.FindAsync(id);
            if (nguoiDung != null)
            {
                // Check if user has bookings
                var hasBookings = await _context.Ves.AnyAsync(v => v.NguoiDungId == id);
                if (hasBookings)
                {
                    TempData["Error"] = "Không thể xóa người dùng đã có vé đặt";
                    return RedirectToAction(nameof(Index));
                }

                try
                {
                    _context.NguoiDungs.Remove(nguoiDung);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Xóa người dùng thành công";
                }
                catch (Exception)
                {
                    TempData["Error"] = "Có lỗi xảy ra khi xóa người dùng";
                }
            }

            return RedirectToAction(nameof(Index));
        }

        private async Task<bool> NguoiDungExists(int id)
        {
            return await _context.NguoiDungs.AnyAsync(e => e.NguoiDungId == id);
        }

        private string HashPassword(string password)
        {
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                byte[] hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        // API để lấy thông tin chuyến xe cho AJAX
        [HttpGet]
        public async Task<IActionResult> GetChuyenXeInfo(int id)
        {
            try
            {
                var chuyenXe = await _context.ChuyenXes
                    .Include(c => c.Xe)
                    .Include(c => c.Ves)
                    .Include(c => c.TuyenDuong)
                    .FirstOrDefaultAsync(c => c.ChuyenXeId == id);

                if (chuyenXe == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy chuyến xe" });
                }

                // Add null check for Xe property
                if (chuyenXe.Xe == null)
                {
                    return Json(new { success = false, message = "Thông tin xe không có sẵn" });
                }

                var result = new
                {
                    success = true,
                    chuyenXe = new
                    {
                        diemDi = chuyenXe.DiemDiDisplay,
                        diemDen = chuyenXe.DiemDenDisplay,
                        ngayKhoiHanh = chuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm"),
                        gia = chuyenXe.Gia.ToString("N0"),
                        bienSo = chuyenXe.Xe.BienSo,
                        loaiXe = chuyenXe.Xe.LoaiXe,
                        soGhe = chuyenXe.Xe.SoGhe,
                        soVeDaBan = chuyenXe.Ves != null ? chuyenXe.Ves.Count : 0,
                        daKhoiHanh = chuyenXe.NgayKhoiHanh <= DateTime.Now
                    }
                };

                return Json(result);
            }
            catch (Exception)
            {
                return Json(new { success = false, message = "Có lỗi xảy ra" });
            }
        }
    }
}
