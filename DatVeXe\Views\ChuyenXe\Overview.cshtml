@{
    ViewData["Title"] = "Tổng quan trang ChuyenXe";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-bus"></i> Tổng quan trang ChuyenXe
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="text-success">✅ Các chức năng đã hoàn thiện:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Danh sách chuyến xe</strong> - Hiển thị tất cả chuyến xe với thông tin đầy đủ
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>T<PERSON><PERSON> kiếm nâng cao</strong> - <PERSON><PERSON><PERSON> theo điểm đi/đến, ngày, giá, loại xe, trạng thái
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Sắp xếp dữ liệu</strong> - Theo ngày, giá, điểm đi/đến, loại xe
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Chi tiết chuyến xe</strong> - Xem thông tin chi tiết, danh sách vé đã đặt
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Thống kê Dashboard</strong> - Báo cáo tổng quan, top tuyến đường
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Quản lý chuyến xe</strong> - Thêm, sửa, xóa chuyến xe (cho Admin)
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Xuất Excel</strong> - Export danh sách chuyến xe
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Chọn chỗ ngồi</strong> - Liên kết với trang đặt vé
                                </li>
                            </ul>

                            <h5 class="text-info mt-4">🔧 Cải tiến đã thực hiện:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-wrench text-info"></i>
                                    <strong>Xử lý lỗi</strong> - Thêm try-catch và thông báo lỗi thân thiện
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-wrench text-info"></i>
                                    <strong>Null safety</strong> - Kiểm tra null cho tất cả properties
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-wrench text-info"></i>
                                    <strong>UI/UX</strong> - Cải thiện giao diện, thêm tooltip, badge trạng thái
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-wrench text-info"></i>
                                    <strong>Performance</strong> - Tối ưu query với Include, lazy loading
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-wrench text-info"></i>
                                    <strong>Responsive</strong> - Giao diện thích ứng với mobile
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-link"></i> Liên kết nhanh
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a asp-action="Index" class="btn btn-primary">
                                            <i class="fas fa-list"></i> Danh sách chuyến xe
                                        </a>
                                        <a asp-action="Dashboard" class="btn btn-info">
                                            <i class="fas fa-chart-bar"></i> Thống kê Dashboard
                                        </a>
                                        <a asp-action="Create" class="btn btn-success">
                                            <i class="fas fa-plus"></i> Thêm chuyến xe mới
                                        </a>
                                        <a asp-controller="SeedData" asp-action="Index" class="btn btn-warning">
                                            <i class="fas fa-database"></i> Quản lý dữ liệu mẫu
                                        </a>
                                        <a asp-controller="Home" asp-action="Index" class="btn btn-outline-secondary">
                                            <i class="fas fa-home"></i> Về trang chủ
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="card bg-light mt-3">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-info-circle"></i> Thông tin kỹ thuật
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <small class="text-muted">
                                        <strong>Controller:</strong> ChuyenXeController<br>
                                        <strong>Model:</strong> ChuyenXeSearchViewModel<br>
                                        <strong>Views:</strong> Index, Details, Dashboard, Create, Edit, Delete<br>
                                        <strong>Features:</strong> CRUD, Search, Filter, Sort, Export<br>
                                        <strong>Database:</strong> Entity Framework Core<br>
                                        <strong>UI Framework:</strong> Bootstrap 5
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Features -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-search"></i> Demo tìm kiếm
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted">Thử các tính năng tìm kiếm:</p>
                    <div class="d-grid gap-2">
                        <a href="/ChuyenXe?DiemDi=TP.HCM" class="btn btn-outline-primary btn-sm">
                            Tìm chuyến từ TP.HCM
                        </a>
                        <a href="/ChuyenXe?DiemDen=Nha Trang" class="btn btn-outline-primary btn-sm">
                            Tìm chuyến đến Nha Trang
                        </a>
                        <a href="/ChuyenXe?LoaiXe=Limousine" class="btn btn-outline-primary btn-sm">
                            Tìm xe Limousine
                        </a>
                        <a href="/ChuyenXe?TrangThai=chua_khoi_hanh" class="btn btn-outline-primary btn-sm">
                            Chuyến chưa khởi hành
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-sort"></i> Demo sắp xếp
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted">Thử các tính năng sắp xếp:</p>
                    <div class="d-grid gap-2">
                        <a href="/ChuyenXe?SortBy=Gia&SortOrder=asc" class="btn btn-outline-success btn-sm">
                            Giá tăng dần
                        </a>
                        <a href="/ChuyenXe?SortBy=Gia&SortOrder=desc" class="btn btn-outline-success btn-sm">
                            Giá giảm dần
                        </a>
                        <a href="/ChuyenXe?SortBy=NgayKhoiHanh&SortOrder=asc" class="btn btn-outline-success btn-sm">
                            Ngày gần nhất
                        </a>
                        <a href="/ChuyenXe?SortBy=DiemDi&SortOrder=asc" class="btn btn-outline-success btn-sm">
                            Điểm đi A-Z
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .list-group-item {
            border: none;
            padding: 0.5rem 0;
        }
        .card {
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .btn {
            transition: all 0.2s;
        }
        .btn:hover {
            transform: translateY(-1px);
        }
    </style>
}
